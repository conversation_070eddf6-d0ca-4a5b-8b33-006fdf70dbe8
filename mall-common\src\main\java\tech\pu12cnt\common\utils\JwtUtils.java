package tech.pu12cnt.common.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.SignatureException;
import lombok.extern.slf4j.Slf4j;
import tech.pu12cnt.common.exception.BusinessException;
import tech.pu12cnt.common.enums.ResponseCode;
import javax.crypto.SecretKey;
import java.util.Date;

/**
 * JWT工具类
 */
@Slf4j
public class JwtUtils {
    
    /**
     * JWT密钥
     */
    private static final String SECRET = "minimall-jwt-secret-key-for-token-generation";
    
    /**
     * JWT过期时间（7天）
     */
    private static final long EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000;
    
    /**
     * 生成密钥
     */
    private static SecretKey getSecretKey() {
        return Keys.hmacShaKeyFor(SECRET.getBytes());
    }
    
    /**
     * 生成JWT Token
     */
    public static String generateToken(Long userId, String username) {
        Date expireDate = DateUtil.offsetMillisecond(new Date(), (int) EXPIRE_TIME);
        
        return Jwts.builder()
                .setSubject(userId.toString())
                .claim("username", username)
                .setIssuedAt(new Date())
                .setExpiration(expireDate)
                .signWith(getSecretKey(), SignatureAlgorithm.HS256)
                .compact();
    }
    
    /**
     * 解析JWT Token
     */
    public static Claims parseToken(String token) {
        if (StrUtil.isBlank(token)) {
            throw new BusinessException(ResponseCode.JWT_INVALID.getCode(), "Token不能为空");
        }
        try {
            return Jwts.parser()
                    .setSigningKey(getSecretKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (ExpiredJwtException e) {
            log.error("JWT Token已过期：{}", e.getMessage());
            throw new BusinessException(ResponseCode.JWT_EXPIRED);
        } catch (SignatureException e) {
            log.error("JWT签名验证失败：{}", e.getMessage());
            throw new BusinessException(ResponseCode.JWT_SIGNATURE_ERROR);
        } catch (MalformedJwtException e) {
            log.error("JWT格式错误：{}", e.getMessage());
            throw new BusinessException(ResponseCode.JWT_MALFORMED);
        } catch (Exception e) {
            log.error("JWT解析失败：{}", e.getMessage());
            throw new BusinessException(ResponseCode.JWT_INVALID.getCode(), "Token解析失败");
        }
    }
    
    /**
     * 从Token中获取用户ID
     */
    public static Long getUserId(String token) {
        Claims claims = parseToken(token);
        if (claims != null) {
            String subject = claims.getSubject();
            if (StrUtil.isNotBlank(subject)) {
                return Long.valueOf(subject);
            }
        }
        return null;
    }
    
    /**
     * 从Token中获取用户名
     */
    public static String getUsername(String token) {
        Claims claims = parseToken(token);
        if (claims != null) {
            return claims.get("username", String.class);
        }
        return null;
    }
    
    
}